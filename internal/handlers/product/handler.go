package product

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"reflect"
	"runtime"
	"sort"
	"strconv"
	"strings"

	"golang.org/x/sync/errgroup"
	"golang.org/x/sync/semaphore"

	"go.uber.org/zap"

	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	"github.com/xuri/excelize/v2"

	"pms-api/internal/domain/product"
	"pms-api/internal/models"
)

// --- 常數定義 ---
const (
	ColPID = iota
	ColProductVAT
	ColProductCompany
	ColGroupName
	ColGroupID
	ColItemID
	ColBrand
	ColName
	ColProductNation
	ColUnitType
	ColCategory
	ColInfo
	ColAuth
	ColProductStep
	ColPrice
	ColPriceInvoice
	ColAuthPC
	ColAuthSVR
	ColAuthCAL
	ColAuthMobile
	ColAuthCore
	ColShipAuth
	ColShipBox
	ColShipDisk
	ColMemo
	ColBidPrice
)

// ExcelHeaders Excel 列標題定義 (順序必須與常量對應)
var ExcelHeaders = []string{
	"PID", "廠商統編", "廠商名稱", "組別名稱", "組別編號", "項次", "廠牌", "品項名稱",
	"產地", "單位", "類別", "功能說明", "單套授權數", "廠商級距", "公開徵求廠商報價",
	"公開徵求發票金額", "PC(電腦)", "SVR(伺服器)", "CAL(終端存取使用權)", "平板手機等行動載具",
	"Core License(核心授權)", "Lic(授權)", "Box(盒裝)", "Disk(光碟)", "備註", "上標決標價",
}

// ValidationError 驗證錯誤結構體
type ValidationError struct {
	Row    int    `json:"row"`    // 發生錯誤的 Excel 行號
	Field  string `json:"field"`  // 發生錯誤的欄位名稱 (Excel 標頭名)
	Value  string `json:"value"`  // 導致錯誤的原始值
	Reason string `json:"reason"` // 錯誤原因的友善描述
}

// APIErrorResponse API 錯誤回傳的標準結構
type APIErrorResponse struct {
	Message string            `json:"message"`
	Details []ValidationError `json:"details,omitempty"`
}

// PreviewResponse API 成功預覽的回應結構
type PreviewResponse struct {
	TotalProcessedRows int               `json:"total_processed_rows"` // 總共處理的非空資料列
	SuccessfulRows     int               `json:"successful_rows"`      // 完全通過驗證的資料列數
	FailedRows         int               `json:"failed_rows"`          // 包含錯誤的資料列數
	Message            string            `json:"message"`
	ValidationErrors   []ValidationError `json:"validation_errors,omitempty"` // 所有驗證錯誤的詳細列表
	//ParsedData         []models.Product  `json:"parsed_data,omitempty"`       // 成功解析並驗證的產品資料
}

// rowParseResult 內部用於在並行處理時傳遞單行結果
type rowParseResult struct {
	product     *models.Product   // 解析且轉換後的 Product (尚未經 struct 級驗證)
	parseErrors []ValidationError // 此行解析轉換階段的錯誤
	excelRowNum int               // Excel 中的原始行號 (1-based)
	isSkipped   bool              // 是否為空行或初始階段即跳過
}

// Handler 介面，定義 HTTP 處理器的契約
type Handler interface {
	PreviewExcel(c echo.Context) error
	UploadProducts(c echo.Context) error

	// ListProducts 獲取產品列表
	//
	// 參數:
	//   - c echo.Context: Echo框架的上下文物件，包含HTTP請求與回應的相關資訊
	//
	// 返回:
	//   - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應:
	//   - 200 OK: 產品列表及分頁資訊
	//   - 400 Bad Request: 請求參數無效
	//   - 401 Unauthorized: 使用者未登入或無權限
	//   - 500 Internal Server Error: 服務器內部處理錯誤
	ListProducts(c echo.Context) error
}

// handler 處理 Excel 匯入的具體實作
type handler struct {
	validator         *validator.Validate
	maxConcurrentRows int64 // 最大並行處理行數

	logger  *zap.Logger
	product productdomain.Service
}

// NewHandler 創建 Product API Handler
func NewHandler(logger *zap.Logger, product productdomain.Service) Handler {
	validate := validator.New()

	// 註冊自訂的欄位名稱轉換函式，使 validator 錯誤訊息能顯示 'excel' tag 的值
	validate.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := fld.Tag.Get("excel")
		if name == "" || name == "-" { // 若無 'excel' tag，則回退到原始欄位名
			return fld.Name
		}
		return name
	})

	// 設定並行處理的上限，例如 CPU 核心數
	// 可依據實際機器配置和任務特性調整
	numCPU := int64(runtime.NumCPU())
	maxConcurrent := numCPU
	if maxConcurrent < 2 {
		maxConcurrent = 2 // 至少保證2個並行通道
	}
	// 也可以設定一個絕對上限，避免資源過度消耗
	// if maxConcurrent > 16 {
	// 	maxConcurrent = 16
	// }

	return &handler{
		validator:         validate,
		maxConcurrentRows: maxConcurrent,
		logger:            logger.Named("Handler").Named("Product"),
		product:           product,
	}
}

// --- HTTP Handler 方法 ---

// PreviewExcel 處理上傳的 Excel 檔案並進行預覽驗證
func (h *handler) PreviewExcel(c echo.Context) error {
	logger := h.logger.Named("PreviewExcel").With(zap.String("ip", c.RealIP()))
	logger.Info("Excel 預覽請求開始處理")

	logger.Info("取得上傳檔案")
	fileHeader, err := c.FormFile("file")
	if err != nil {
		logger.Error("取得上傳檔案失敗", zap.Error(err))
		return c.JSON(http.StatusBadRequest, APIErrorResponse{Message: "錯誤：無法取得上傳的檔案。"})
	}

	if fileHeader.Size > 30*1024*1024 { // 30MB
		logger.Error("檔案大小超過限制", zap.Int64("fileSize", fileHeader.Size))
		return c.JSON(http.StatusBadRequest, APIErrorResponse{Message: "檔案大小超過限制，請上傳小於 30MB 的檔案。"})
	}

	logger.Info("驗證檔案擴展名")
	// 驗證檔案擴展名
	if err = h.validateFileExtension(fileHeader.Filename); err != nil {
		// 為 .xls 格式提供特殊友善提示
		if strings.Contains(err.Error(), ".xls 格式") {
			return c.JSON(http.StatusBadRequest, map[string]any{
				"error":   "不支援舊版 .xls 格式",
				"message": "為了確保最佳的相容性和資料完整性，請將檔案轉換為 .xlsx 格式後再上傳。",
				"instructions": []string{
					"1. 在 Excel 中開啟您的 .xls 檔案。",
					"2. 點選「檔案」→「另存新檔」。",
					"3. 在「檔案類型」中選擇「Excel 活頁簿 (.xlsx)」。",
					"4. 儲存後重新上傳 .xlsx 檔案。",
				},
			})
		}

		logger.Error("檔案擴展名驗證失敗 為了確保最佳的相容性和資料完整性，請將檔案轉換為 .xlsx 格式後再上傳。", zap.Error(err))
		return c.JSON(http.StatusBadRequest, APIErrorResponse{Message: err.Error()})
	}

	logger.Info("開啟上傳檔案")
	// 開啟檔案
	src, err := fileHeader.Open()
	if err != nil {
		logger.Error("開啟上傳檔案失敗", zap.Error(err))
		c.Logger().Errorf("開啟上傳檔案 '%s' 失敗: %v", fileHeader.Filename, err)
		return c.JSON(http.StatusInternalServerError, APIErrorResponse{Message: "伺服器內部錯誤：無法開啟檔案。"})
	}

	logger.Info("使用 excelize 開啟檔案")
	// 使用 excelize 開啟檔案流
	xlsxFile, err := excelize.OpenReader(src)
	if err != nil {
		logger.Error("使用 excelize 開啟檔案失敗", zap.Error(err))
		c.Logger().Errorf("使用 excelize 開啟檔案 '%s' 失敗: %v", fileHeader.Filename, err)
		return c.JSON(http.StatusBadRequest, APIErrorResponse{Message: fmt.Sprintf("無法讀取 Excel 檔案，請確認檔案為標準 .xlsx 格式且未損毀。錯誤詳情：%s", err.Error())})
	}

	if closeErr := src.Close(); closeErr != nil {
		logger.Error("關閉上傳檔案時發生錯誤", zap.Error(closeErr))
		c.Logger().Errorf("關閉上傳檔案 '%s' 時發生錯誤: %v", fileHeader.Filename, closeErr)
	}

	logger.Info("取得第一個工作表名稱")
	// 取得第一個工作表的名稱
	sheetName, err := h.getFirstSheetName(xlsxFile)
	if err != nil {
		logger.Error("取得第一個工作表名稱失敗", zap.Error(err))
		return c.JSON(http.StatusBadRequest, APIErrorResponse{Message: err.Error()})
	}

	logger.Info("讀取工作表所有行")
	// 讀取工作表所有行
	rows, err := xlsxFile.GetRows(sheetName)
	if err != nil {
		logger.Error("讀取工作表資料失敗", zap.Error(err))
		c.Logger().Errorf("讀取工作表 '%s' 資料失敗: %v", sheetName, err)
		return c.JSON(http.StatusInternalServerError, APIErrorResponse{Message: fmt.Sprintf("讀取 Excel 工作表 '%s' 資料時發生錯誤。", sheetName)})
	}

	if err = xlsxFile.Close(); err != nil {
		logger.Error("關閉 Excel 檔案失敗", zap.Error(err))
		c.Logger().Errorf("關閉 Excel 檔案 '%s' 時發生錯誤: %v", fileHeader.Filename, err)
		return c.JSON(http.StatusInternalServerError, APIErrorResponse{Message: "伺服器內部錯誤：無法關閉 Excel 檔案。"})
	}

	logger.Info("基本檢查：是否有標頭行")
	// 基本檢查：是否有標頭行
	if len(rows) == 0 {
		logger.Info("Excel 文件為空，不包含任何資料列")
		return c.JSON(http.StatusOK, PreviewResponse{Message: "Excel 文件為空，不包含任何資料列。"})
	}

	logger.Info("驗證標頭行")
	// 驗證標頭行
	headerRow := rows[0]
	headerValidationErrors := h.validateHeaders(headerRow, ExcelHeaders)
	if len(headerValidationErrors) > 0 {
		logger.Error("Excel 檔案的標頭不符合預期格式", zap.Any("errors", headerValidationErrors))
		return c.JSON(http.StatusBadRequest, APIErrorResponse{
			Message: "Excel 檔案的標頭不符合預期格式，請修正後重新上傳。",
			Details: headerValidationErrors,
		})
	}

	logger.Info("開始處理資料行")
	// 處理資料行 (跳過標頭)
	dataRows := rows[1:]
	if len(dataRows) == 0 {
		logger.Info("Excel 文件僅包含標頭列，沒有實際資料內容")
		return c.JSON(http.StatusOK, PreviewResponse{Message: "Excel 文件僅包含標頭列，沒有實際資料內容。"})
	}

	logger.Info("開始並行處理資料列")
	// --- 並行處理資料列 ---
	// 使用 errgroup 管理 Goroutine 生命週期並處理 context
	g, gCtx := errgroup.WithContext(c.Request().Context())
	// 使用 semaphore 限制並行處理的 Goroutine 數量
	sem := semaphore.NewWeighted(h.maxConcurrentRows)

	// 用於收集每個 Goroutine 處理結果的 Channel
	// Bufferred channel, 大小設為資料行數，避免某些情況下發送阻塞 (儘管有 semaphore 控制接收速率)
	resultsChan := make(chan rowParseResult, len(dataRows))

	for i, rowData := range dataRows {
		// 嘗試獲取一個 semaphore "權杖"，如果 context 被取消則會失敗
		if err = sem.Acquire(gCtx, 1); err != nil {
			logger.Error("無法獲取 semaphore goroutine", zap.Error(err))
			c.Logger().Warnf("無法獲取 semaphore goroutine (第 %d 行): %v。可能請求已取消或超時。", i+2, err)
			// 若無法獲取 (通常因 context 取消)，則停止派發新任務
			// gCtx.Err() 將會由 g.Wait() 捕獲
			break
		}

		// 捕獲迴圈變數，確保每個 Goroutine 使用正確的索引和資料
		currentIndex := i
		currentRowData := rowData
		currentExcelRowNum := currentIndex + 2 // Excel 行號從1開始，加上標頭行是第1行

		g.Go(func() error {
			defer sem.Release(1) // Goroutine 完成後釋放 "權杖"

			// 檢查 context 是否已被取消，若是則提前返回
			select {
			case <-gCtx.Done():
				logger.Error("context 已被取消，提前返回", zap.Error(gCtx.Err()))
				// 發送一個標記，表示此行未被處理 (或被中斷)
				// resultsChan <- rowParseResult{excelRowNum: currentExcelRowNum, isSkipped: true}
				// 不需要發送，因為主循環的 Acquire 會先失敗
				return gCtx.Err() // 向 errgroup 回報 context 錯誤
			default:
				// Context 正常，繼續執行任務
			}

			// 檢查是否為空行
			if h.isEmptyRow(currentRowData, len(ExcelHeaders)) {
				resultsChan <- rowParseResult{excelRowNum: currentExcelRowNum, isSkipped: true}
				return nil
			}

			// 解析單行資料 (轉換 + 初始欄位級驗證)
			product, parseErrs := h.parseRowToProduct(currentRowData, currentExcelRowNum)
			resultsChan <- rowParseResult{
				product:     product,
				parseErrors: parseErrs,
				excelRowNum: currentExcelRowNum,
			}
			return nil
		})
	}

	// 等待所有由 errgroup 啟動的 Goroutine 完成
	groupErr := g.Wait()
	close(resultsChan) // 所有發送方 Goroutine 都已結束，安全關閉 Channel

	// 如果 groupErr 不是 nil，通常表示 context 被取消，或者某個 Goroutine 返回了錯誤
	// (在此設計中，Goroutine 內部錯誤透過 resultsChan 回傳，除非是 context 問題)
	if groupErr != nil && !errors.Is(groupErr, context.Canceled) && !errors.Is(groupErr, context.DeadlineExceeded) {
		logger.Error("並行處理 Excel 資料列時發生非預期的 errgroup 錯誤", zap.Error(groupErr))
		c.Logger().Errorf("並行處理 Excel 資料列時發生非預期的 errgroup 錯誤: %v", groupErr)
		// 可選擇是否在此中斷並回傳錯誤，或者繼續處理已收集到的結果
	}

	// --- 收集並整合處理結果 ---
	var allValidationErrors []ValidationError
	totalProcessed := 0
	successfulRows := 0
	failedRows := 0

	logger.Info("收集並整合處理結果")
	for res := range resultsChan {
		if res.isSkipped {
			continue // 跳過空行或未處理的行
		}
		totalProcessed++

		// 先收集解析階段的錯誤
		if len(res.parseErrors) > 0 {
			allValidationErrors = append(allValidationErrors, res.parseErrors...)
			// 即使解析有錯，也可能需要 product 的部分資訊，但這裡假設解析錯誤即為失敗
		}

		// 只有當解析階段沒有錯誤時，才進行 struct 級驗證
		if res.product != nil && len(res.parseErrors) == 0 {
			// 執行 struct 級驗證 (validator tags)
			if err = h.validator.Struct(res.product); err != nil {
				// 如果是 validator.ValidationErrors 類型，表示有多個欄位驗證失敗
				var vErrs validator.ValidationErrors
				if errors.As(err, &vErrs) {
					for _, vErr := range vErrs {
						allValidationErrors = append(allValidationErrors, ValidationError{
							Row:    res.excelRowNum,
							Field:  vErr.Field(), // 已透過 RegisterTagNameFunc 處理，顯示 'excel' tag
							Value:  fmt.Sprintf("%v", vErr.Value()),
							Reason: h.translateValidatorError(vErr), // 轉譯成友善訊息
						})
					}
				}
			}
		}
	}

	logger.Info("計算最終的成功/失敗行數")
	// 計算最終的成功/失敗行數 (一個行只要有一個錯誤就算失敗)
	errorRowsMap := make(map[int]bool)
	for _, verr := range allValidationErrors {
		errorRowsMap[verr.Row] = true
	}
	failedRows = len(errorRowsMap)
	successfulRows = totalProcessed - failedRows
	if successfulRows < 0 {
		successfulRows = 0
	} // 以防萬一

	// 對驗證錯誤按行號排序，方便查看
	sort.Slice(allValidationErrors, func(i, j int) bool {
		if allValidationErrors[i].Row == allValidationErrors[j].Row {
			return getHeaderIndex(allValidationErrors[i].Field) < getHeaderIndex(allValidationErrors[j].Field)
		}
		return allValidationErrors[i].Row < allValidationErrors[j].Row
	})

	// 準備最終回應
	finalMessage := ""
	if totalProcessed == 0 {
		finalMessage = "未處理任何有效的資料列 (可能檔案僅含標頭，或所有資料列都為空)。"
	} else if failedRows > 0 {
		finalMessage = fmt.Sprintf("共處理 %d 個有效資料列：成功 %d 列，失敗 %d 列。請檢查錯誤詳情。",
			totalProcessed, successfulRows, failedRows)
	} else {
		finalMessage = fmt.Sprintf("成功預覽並驗證 %d 筆資料，所有資料均符合格式。", successfulRows)
	}

	return c.JSON(http.StatusOK, PreviewResponse{
		TotalProcessedRows: totalProcessed,
		SuccessfulRows:     successfulRows,
		FailedRows:         failedRows,
		Message:            finalMessage,
		ValidationErrors:   allValidationErrors,
	})
}

func (h *handler) UploadProducts(c echo.Context) error {
	logger := h.logger.Named("UploadProducts").With(zap.String("ip", c.RealIP()))
	logger.Info("Excel 匯入請求開始處理")

	var userID uint32
	// 從上下文中獲取使用者ID和角色
	//if userIDValue := c.Get(models.UserID); userIDValue != nil {
	//	userID = userIDValue.(uint32)
	//} else {
	//	logger.Error("無法獲取使用者ID")
	//}

	switch v := c.Get(models.UserID).(type) {
	case uint32:
		userID = v
	default:
		logger.Error("無法獲取使用者ID")
		return echo.NewHTTPError(http.StatusUnauthorized, "請先登入")
	}

	logger.Info("取得上傳檔案")
	fileHeader, err := c.FormFile("file")
	if err != nil {
		logger.Error("取得上傳檔案失敗", zap.Error(err))
		return c.JSON(http.StatusBadRequest, APIErrorResponse{Message: "錯誤：無法取得上傳的檔案。"})
	}

	if fileHeader.Size > 30*1024*1024 { // 30MB
		logger.Error("檔案大小超過限制", zap.Int64("fileSize", fileHeader.Size))
		return c.JSON(http.StatusBadRequest, APIErrorResponse{Message: "檔案大小超過限制，請上傳小於 30MB 的檔案。"})
	}

	logger.Info("驗證檔案擴展名")
	// 驗證檔案擴展名
	if err = h.validateFileExtension(fileHeader.Filename); err != nil {
		// 為 .xls 格式提供特殊友善提示
		if strings.Contains(err.Error(), ".xls 格式") {
			return c.JSON(http.StatusBadRequest, map[string]interface{}{
				"error":   "不支援舊版 .xls 格式",
				"message": "為了確保最佳的相容性和資料完整性，請將檔案轉換為 .xlsx 格式後再上傳。",
				"instructions": []string{
					"1. 在 Excel 中開啟您的 .xls 檔案。",
					"2. 點選「檔案」→「另存新檔」。",
					"3. 在「檔案類型」中選擇「Excel 活頁簿 (.xlsx)」。",
					"4. 儲存後重新上傳 .xlsx 檔案。",
				},
			})
		}

		logger.Error("檔案擴展名驗證失敗 為了確保最佳的相容性和資料完整性，請將檔案轉換為 .xlsx 格式後再上傳。", zap.Error(err))
		return c.JSON(http.StatusBadRequest, APIErrorResponse{Message: err.Error()})
	}

	logger.Info("開啟上傳檔案")
	// 開啟檔案
	src, err := fileHeader.Open()
	if err != nil {
		logger.Error("開啟上傳檔案失敗", zap.Error(err))
		c.Logger().Errorf("開啟上傳檔案 '%s' 失敗: %v", fileHeader.Filename, err)
		return c.JSON(http.StatusInternalServerError, APIErrorResponse{Message: "伺服器內部錯誤：無法開啟檔案。"})
	}

	logger.Info("使用 excelize 開啟檔案")
	// 使用 excelize 開啟檔案流
	xlsxFile, err := excelize.OpenReader(src)
	if err != nil {
		logger.Error("使用 excelize 開啟檔案失敗", zap.Error(err))
		c.Logger().Errorf("使用 excelize 開啟檔案 '%s' 失敗: %v", fileHeader.Filename, err)
		return c.JSON(http.StatusBadRequest, APIErrorResponse{Message: fmt.Sprintf("無法讀取 Excel 檔案，請確認檔案為標準 .xlsx 格式且未損毀。錯誤詳情：%s", err.Error())})
	}

	if closeErr := src.Close(); closeErr != nil {
		logger.Error("關閉上傳檔案時發生錯誤", zap.Error(closeErr))
		c.Logger().Errorf("關閉上傳檔案 '%s' 時發生錯誤: %v", fileHeader.Filename, closeErr)
	}

	logger.Info("取得第一個工作表名稱")
	// 取得第一個工作表的名稱
	sheetName, err := h.getFirstSheetName(xlsxFile)
	if err != nil {
		logger.Error("取得第一個工作表名稱失敗", zap.Error(err))
		return c.JSON(http.StatusBadRequest, APIErrorResponse{Message: err.Error()})
	}

	logger.Info("讀取工作表所有行")
	// 讀取工作表所有行
	rows, err := xlsxFile.GetRows(sheetName)
	if err != nil {
		logger.Error("讀取工作表資料失敗", zap.Error(err))
		c.Logger().Errorf("讀取工作表 '%s' 資料失敗: %v", sheetName, err)
		return c.JSON(http.StatusInternalServerError, APIErrorResponse{Message: fmt.Sprintf("讀取 Excel 工作表 '%s' 資料時發生錯誤。", sheetName)})
	}

	if err = xlsxFile.Close(); err != nil {
		logger.Error("關閉 Excel 檔案失敗", zap.Error(err))
		c.Logger().Errorf("關閉 Excel 檔案 '%s' 時發生錯誤: %v", fileHeader.Filename, err)
		return c.JSON(http.StatusInternalServerError, APIErrorResponse{Message: "伺服器內部錯誤：無法關閉 Excel 檔案。"})
	}

	logger.Info("開始處理資料行")
	// 處理資料行 (跳過標頭)
	dataRows := rows[1:]
	if len(dataRows) == 0 {
		logger.Info("Excel 文件僅包含標頭列，沒有實際資料內容")
		return c.JSON(http.StatusOK, PreviewResponse{Message: "Excel 文件僅包含標頭列，沒有實際資料內容。"})
	}

	logger.Info("開始並行處理資料列")
	// --- 並行處理資料列 ---
	// 使用 errgroup 管理 Goroutine 生命週期並處理 context
	g, gCtx := errgroup.WithContext(c.Request().Context())
	// 使用 semaphore 限制並行處理的 Goroutine 數量
	sem := semaphore.NewWeighted(h.maxConcurrentRows)

	// 用於收集每個 Goroutine 處理結果的 Channel
	// Bufferred channel, 大小設為資料行數，避免某些情況下發送阻塞 (儘管有 semaphore 控制接收速率)
	resultsChan := make(chan rowParseResult, len(dataRows))

	for i, rowData := range dataRows {
		// 嘗試獲取一個 semaphore "權杖"，如果 context 被取消則會失敗
		if err := sem.Acquire(gCtx, 1); err != nil {
			logger.Error("無法獲取 semaphore goroutine", zap.Error(err))
			c.Logger().Warnf("無法獲取 semaphore goroutine (第 %d 行): %v。可能請求已取消或超時。", i+2, err)
			// 若無法獲取 (通常因 context 取消)，則停止派發新任務
			// gCtx.Err() 將會由 g.Wait() 捕獲
			break
		}

		// 捕獲迴圈變數，確保每個 Goroutine 使用正確的索引和資料
		currentIndex := i
		currentRowData := rowData
		currentExcelRowNum := currentIndex + 2 // Excel 行號從1開始，加上標頭行是第1行

		g.Go(func() error {
			defer sem.Release(1) // Goroutine 完成後釋放 "權杖"

			// 檢查 context 是否已被取消，若是則提前返回
			select {
			case <-gCtx.Done():
				logger.Error("context 已被取消，提前返回", zap.Error(gCtx.Err()))
				// 發送一個標記，表示此行未被處理 (或被中斷)
				// resultsChan <- rowParseResult{excelRowNum: currentExcelRowNum, isSkipped: true}
				// 不需要發送，因為主循環的 Acquire 會先失敗
				return gCtx.Err() // 向 errgroup 回報 context 錯誤
			default:
				// Context 正常，繼續執行任務
			}

			// 檢查是否為空行
			if h.isEmptyRow(currentRowData, len(ExcelHeaders)) {
				resultsChan <- rowParseResult{excelRowNum: currentExcelRowNum, isSkipped: true}
				return nil
			}

			// 解析單行資料 (轉換 + 初始欄位級驗證)
			product, parseErrs := h.parseRowToProduct(currentRowData, currentExcelRowNum)
			resultsChan <- rowParseResult{
				product:     product,
				parseErrors: parseErrs,
				excelRowNum: currentExcelRowNum,
			}
			return nil
		})
	}

	// 等待所有由 errgroup 啟動的 Goroutine 完成
	groupErr := g.Wait()
	close(resultsChan) // 所有發送方 Goroutine 都已結束，安全關閉 Channel

	// 如果 groupErr 不是 nil，通常表示 context 被取消，或者某個 Goroutine 返回了錯誤
	// (在此設計中，Goroutine 內部錯誤透過 resultsChan 回傳，除非是 context 問題)
	if groupErr != nil && !errors.Is(groupErr, context.Canceled) && !errors.Is(groupErr, context.DeadlineExceeded) {
		logger.Error("並行處理 Excel 資料列時發生非預期的 errgroup 錯誤", zap.Error(groupErr))
		c.Logger().Errorf("並行處理 Excel 資料列時發生非預期的 errgroup 錯誤: %v", groupErr)
		// 可選擇是否在此中斷並回傳錯誤，或者繼續處理已收集到的結果
	}

	// --- 收集並整合處理結果 ---
	var allParsedProducts []*models.Product
	totalProcessed := 0

	logger.Info("收集並整合處理結果")
	for res := range resultsChan {
		if res.isSkipped {
			continue // 跳過空行或未處理的行
		}
		totalProcessed++

		// 只有當解析階段沒有錯誤時，才進行 struct 級驗證
		if res.product != nil && len(res.parseErrors) == 0 {
			res.product.ExcelRowIndex = res.excelRowNum // 儲存原始行號
			allParsedProducts = append(allParsedProducts, res.product)
		}
	}

	if err = h.product.CreateMany(c.Request().Context(), allParsedProducts, userID); err != nil {
		logger.Error("批量創建產品失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "批量創建產品失敗")
	}

	return c.NoContent(http.StatusOK)
}

// --- 輔助方法 ---

// validateFileExtension 檢查檔案擴展名是否為 .xlsx
func (h *handler) validateFileExtension(filename string) error {
	lowerFilename := strings.ToLower(filename)
	if strings.HasSuffix(lowerFilename, ".xls") {
		return fmt.Errorf("不支援舊版 .xls 格式")
	}
	if !strings.HasSuffix(lowerFilename, ".xlsx") {
		return fmt.Errorf("檔案格式錯誤：僅支援 .xlsx 格式的 Excel 檔案。")
	}
	return nil
}

// getFirstSheetName 安全地取得 Excel 檔案中的第一個工作表名稱
func (h *handler) getFirstSheetName(xlsxFile *excelize.File) (string, error) {

	sheetList := xlsxFile.GetSheetList()
	if len(sheetList) == 0 {
		return "", fmt.Errorf("excel 檔案中沒有找到任何工作表。")
	}
	// excelize V2.0.0 後 GetSheetName(0) 和 GetSheetName(1) 都是有效的，但用列表第一個最直觀
	return sheetList[0], nil
}

// validateHeaders 驗證 Excel 檔案的標頭行是否符合預期
func (h *handler) validateHeaders(headerRow []string, expectedHeaders []string) []ValidationError {

	var validationErrors []ValidationError

	// 清理讀取到的標頭中的潛在不可見字符或多餘空格
	cleanedHeaderRow := make([]string, len(headerRow))
	for i, hVal := range headerRow {
		cleanedHeaderRow[i] = strings.TrimSpace(strings.ReplaceAll(strings.ReplaceAll(hVal, "\n", ""), "\r", ""))
	}

	// 檢查欄位數量是否至少與預期一致 (允許 Excel 有額外欄位，但不檢查)
	if len(cleanedHeaderRow) < len(expectedHeaders) {
		validationErrors = append(validationErrors, ValidationError{
			Row:    1, // 標頭行
			Field:  "標題列整體",
			Value:  fmt.Sprintf("實際 %d 個", len(cleanedHeaderRow)),
			Reason: fmt.Sprintf("欄位數量不足。期望至少 %d 個欄位，請下載最新的範例檔案核對。", len(expectedHeaders)),
		})
		return validationErrors // 數量不足是嚴重問題，後續比較意義不大
	}

	// 逐個比較預期範圍內的標頭名稱
	for i, expected := range expectedHeaders {
		actual := cleanedHeaderRow[i]
		if actual != expected {
			validationErrors = append(validationErrors, ValidationError{
				Row:    1,
				Field:  fmt.Sprintf("第 %d 欄", i+1),
				Value:  actual, // 錯誤的值是實際讀到的標頭
				Reason: fmt.Sprintf("標頭名稱不符。期望為「%s」，請修正或下載範例檔案。", expected),
			})
		}
	}
	return validationErrors
}

// isEmptyRow 檢查一行資料（字串切片）是否所有元素都為空或僅含空白字元
func (h *handler) isEmptyRow(row []string, expectedCols int) bool {
	// 只需要檢查到預期欄位的最大數量即可，之後的多餘欄位不影響判斷是否為空行
	// (此邏輯也可以簡化為只要 row 中有任一非空即可)
	for i := 0; i < len(row) && i < expectedCols; i++ {
		if strings.TrimSpace(row[i]) != "" {
			return false // 找到非空元素
		}
	}
	return true // 所有檢查的元素都為空
}

// getHeaderIndex 根據標頭名稱獲取其在 ExcelHeaders 中的索引，用於排序
func getHeaderIndex(headerName string) int {
	for i, h := range ExcelHeaders {
		if h == headerName {
			return i
		}
	}
	// 如果是 "資料結構" 或其他非ExcelHeader的欄位名，可以給一個較大的索引
	if headerName == "資料結構" {
		return len(ExcelHeaders) + 1
	}
	// 如果找不到（理論上不應該發生，因為 Field 應該來自 ExcelHeaders 或 'excel' tag）
	// 給一個很大的值，讓它排在後面
	return len(ExcelHeaders) + 100
}

// --- 單行資料解析與轉換輔助函式 ---

// safeGetCellValue 安全地從一行資料中取得指定索引的儲存格字串值，並去除首尾空白
func safeGetCellValue(row []string, colIndex int) string {
	if colIndex >= len(row) {
		return ""
	}
	return strings.TrimSpace(row[colIndex])
}

// parseToInt32 將字串轉換為 int32。如果轉換失敗，會將錯誤加入到 errors 切片中。
func parseToInt32(valStr string, fieldHeader string, rowNum int, errors *[]ValidationError) (int32, bool) {

	if valStr == "" {
		return 0, true // 空字串視為 0，成功
	}
	valStr = strings.ReplaceAll(valStr, ",", "")
	i, err := strconv.ParseInt(valStr, 10, 32)
	if err != nil {
		*errors = append(*errors, ValidationError{
			Row: rowNum, Field: fieldHeader, Value: valStr, Reason: "應為有效的整數數字。",
		})
		return 0, false // 轉換失敗
	}
	return int32(i), true
}

func parseToUint32(valStr string, fieldHeader string, rowNum int, errors *[]ValidationError) (uint32, bool) {
	if valStr == "" {
		return 0, true // 空字串視為 0，成功
	}
	valStr = strings.ReplaceAll(valStr, ",", "")
	i, err := strconv.ParseUint(valStr, 10, 32)
	if err != nil {
		*errors = append(*errors, ValidationError{
			Row: rowNum, Field: fieldHeader, Value: valStr, Reason: "應為有效的正整數數字。",
		})
		return 0, false // 轉換失敗
	}
	return uint32(i), true
}

func parseToInt32Ptr(valStr string, fieldHeader string, rowNum int, errors *[]ValidationError) (*int32, bool) {
	if valStr == "" {
		return nil, true // 空字串視為 nil，成功
	}
	valStr = strings.ReplaceAll(valStr, ",", "")
	i, err := strconv.ParseInt(valStr, 10, 32)
	if err != nil {
		*errors = append(*errors, ValidationError{
			Row: rowNum, Field: fieldHeader, Value: valStr, Reason: "應為有效的整數數字或留空。",
		})
		return nil, false
	}
	i32 := int32(i)
	return &i32, true
}

// parseToBoolLikeInt 將字串轉換為代表布林 (0 或 1) 的 int。空字串視為 0。失敗時將錯誤加入 errors。
// 這裡不使用 Product 結構上的 validator oneof 是因為我們想在解析階段就處理 "空值=0" 的情況。
func parseToBoolLikeInt(valStr string, fieldHeader string, rowNum int, errors *[]ValidationError) (int, bool) {
	if valStr == "" {
		return 0, true // 空字串視為 0，成功
	}
	i, err := strconv.Atoi(valStr)
	if err != nil || (i != 0 && i != 1) {
		*errors = append(*errors, ValidationError{
			Row: rowNum, Field: fieldHeader, Value: valStr, Reason: "值應為 0 (否) 或 1 (是)，或留空 (視為0)。",
		})
		// 即使驗證失敗，也可能返回一個預設值如0，視需求而定
		return 0, false
	}
	return i, true
}

// parseProductStep 解析格式為 "數字-數字" 的廠商級距。
func parseProductStep(stepStr string, fieldHeader string, rowNum int, errors *[]ValidationError) (*int32, *int32, bool) {
	if stepStr == "" {
		return nil, nil, true // 空白表示無級距，有效
	}

	if !strings.Contains(stepStr, "-") {
		*errors = append(*errors, ValidationError{
			Row: rowNum, Field: fieldHeader, Value: stepStr, Reason: "格式不正確，應為「數字-數字」(例如 1-100) 或空白。",
		})
		return nil, nil, false
	}

	parts := strings.Split(stepStr, "-")
	if len(parts) != 2 {
		*errors = append(*errors, ValidationError{
			Row: rowNum, Field: fieldHeader, Value: stepStr, Reason: "格式不正確，應剛好包含一個「-」符號分隔兩個數字。",
		})
		return nil, nil, false
	}

	startStr := strings.TrimSpace(parts[0])
	endStr := strings.TrimSpace(parts[1])

	var startVal, endVal int32
	var startOk, endOk bool

	startSubField := fieldHeader + " (起始值)" // 更精確的錯誤欄位
	endSubField := fieldHeader + " (結束值)"

	// 解析起始值
	if startStr == "" {
		*errors = append(*errors, ValidationError{Row: rowNum, Field: startSubField, Value: startStr, Reason: "級距的起始值不可為空。"})
	} else {
		startVal, startOk = parseToInt32(startStr, startSubField, rowNum, errors)
		if startOk && startVal < 0 { // 通常級距不為負
			*errors = append(*errors, ValidationError{Row: rowNum, Field: startSubField, Value: startStr, Reason: "級距的起始值不可為負數。"})
			startOk = false
		}
	}

	// 解析結束值
	if endStr == "" {
		*errors = append(*errors, ValidationError{Row: rowNum, Field: endSubField, Value: endStr, Reason: "級距的結束值不可為空。"})
	} else {
		endVal, endOk = parseToInt32(endStr, endSubField, rowNum, errors)
		if endOk && endVal < 0 {
			*errors = append(*errors, ValidationError{Row: rowNum, Field: endSubField, Value: endStr, Reason: "級距的結束值不可為負數。"})
			endOk = false
		}
	}

	if !startOk || !endOk { // 任一解析失敗
		return nil, nil, false
	}

	// 業務邏輯驗證：結束值必須大於起始值
	if endVal <= startVal {
		*errors = append(*errors, ValidationError{
			Row: rowNum, Field: fieldHeader, Value: stepStr, Reason: "級距的最大值(結束值)必須嚴格大於最小值(起始值)。",
		})
		// 即使邏輯錯誤，也返回已解析的值，但整體標記為失敗
		return &startVal, &endVal, false
	}

	return &startVal, &endVal, true
}

// parseRowToProduct 將 Excel 的一行資料解析並轉換為 Product 結構。
// 此階段僅進行欄位格式轉換和基本的欄位級驗證。結構級驗證 (validator tags) 稍後進行。
func (h *handler) parseRowToProduct(row []string, excelRowNum int) (*models.Product, []ValidationError) {
	var parseErrors []ValidationError // 收集此行在解析轉換階段的錯誤
	p := &models.Product{}            // 初始化一個空的 Product 物件

	// 使用一個模式來簡化每個欄位的解析和錯誤處理
	// (fieldHeader, parseFunc) -> value, ok
	// 欄位名直接從 ExcelHeaders 取得，確保與常量索引同步

	// PID
	p.PID, _ = parseToInt32(safeGetCellValue(row, ColPID), ExcelHeaders[ColPID], excelRowNum, &parseErrors)
	// ProductVAT (字串)
	p.ProductVAT = safeGetCellValue(row, ColProductVAT)
	// ProductCompany (字串)
	p.ProductCompany = safeGetCellValue(row, ColProductCompany)
	// GroupName (字串)
	p.GroupName = safeGetCellValue(row, ColGroupName)
	// GroupID
	p.GroupID, _ = parseToUint32(safeGetCellValue(row, ColGroupID), ExcelHeaders[ColGroupID], excelRowNum, &parseErrors)
	// ItemID
	p.ItemID, _ = parseToInt32(safeGetCellValue(row, ColItemID), ExcelHeaders[ColItemID], excelRowNum, &parseErrors)
	// Brand (字串)
	p.Brand = safeGetCellValue(row, ColBrand)
	// Name (字串, required 由 validator 處理)
	p.Name = safeGetCellValue(row, ColName)
	// ProductNation (字串)
	p.ProductNation = safeGetCellValue(row, ColProductNation)
	// UnitType (字串)
	p.UnitType = safeGetCellValue(row, ColUnitType)

	// Category (特殊邏輯)
	categoryStr := safeGetCellValue(row, ColCategory)
	if categoryStr == "新增品項" || categoryStr == "1" || categoryStr == "" {
		p.Category = "新增品項"
	} else if categoryStr == "新增級距" || categoryStr == "2" {
		p.Category = "新增級距"
	} else {
		parseErrors = append(parseErrors, ValidationError{
			Row: excelRowNum, Field: ExcelHeaders[ColCategory], Value: categoryStr,
			Reason: "內容無效，應為「新增品項」(或1/空白) 或「新增級距」(或2)。",
		})
		p.Category = "新增品項" // 出錯時給一個預設值，或根據需求處理
	}

	// Info (字串)
	p.Info = safeGetCellValue(row, ColInfo)
	// Auth (指標 int)
	p.Auth, _ = parseToInt32Ptr(safeGetCellValue(row, ColAuth), ExcelHeaders[ColAuth], excelRowNum, &parseErrors)

	// ProductStep (廠商級距，特殊解析)
	stepStr := safeGetCellValue(row, ColProductStep)
	p.StepStart, p.StepEnd, _ = parseProductStep(stepStr, ExcelHeaders[ColProductStep], excelRowNum, &parseErrors)

	// Price (指標 int)
	p.Price, _ = parseToInt32Ptr(safeGetCellValue(row, ColPrice), ExcelHeaders[ColPrice], excelRowNum, &parseErrors)
	// PriceInvoice (指標 int)
	p.PriceInvoice, _ = parseToInt32Ptr(safeGetCellValue(row, ColPriceInvoice), ExcelHeaders[ColPriceInvoice], excelRowNum, &parseErrors)

	// Bool-like int (0/1) 欄位
	p.AuthPC, _ = parseToBoolLikeInt(safeGetCellValue(row, ColAuthPC), ExcelHeaders[ColAuthPC], excelRowNum, &parseErrors)
	p.AuthSVR, _ = parseToBoolLikeInt(safeGetCellValue(row, ColAuthSVR), ExcelHeaders[ColAuthSVR], excelRowNum, &parseErrors)
	p.AuthCAL, _ = parseToBoolLikeInt(safeGetCellValue(row, ColAuthCAL), ExcelHeaders[ColAuthCAL], excelRowNum, &parseErrors)
	p.AuthMobile, _ = parseToBoolLikeInt(safeGetCellValue(row, ColAuthMobile), ExcelHeaders[ColAuthMobile], excelRowNum, &parseErrors)
	p.AuthCore, _ = parseToBoolLikeInt(safeGetCellValue(row, ColAuthCore), ExcelHeaders[ColAuthCore], excelRowNum, &parseErrors)
	p.ShipAuth, _ = parseToBoolLikeInt(safeGetCellValue(row, ColShipAuth), ExcelHeaders[ColShipAuth], excelRowNum, &parseErrors)
	p.ShipBox, _ = parseToBoolLikeInt(safeGetCellValue(row, ColShipBox), ExcelHeaders[ColShipBox], excelRowNum, &parseErrors)
	p.ShipDisk, _ = parseToBoolLikeInt(safeGetCellValue(row, ColShipDisk), ExcelHeaders[ColShipDisk], excelRowNum, &parseErrors)

	// Memo (字串)
	p.Memo = safeGetCellValue(row, ColMemo)
	// BidPrice (字串)
	p.BidPrice = safeGetCellValue(row, ColBidPrice)

	if len(parseErrors) > 0 {
		// 即使有解析錯誤，也回傳部分解析的 product 物件，可能對 debug 有用，
		// 或者，如果策略是任何解析錯誤都使 product 無效，則可回傳 nil product
		return p, parseErrors
	}
	return p, nil // 解析成功，沒有錯誤
}

// translateValidatorError 將 validator.FieldError 轉換為繁體中文錯誤原因
func (h *handler) translateValidatorError(fe validator.FieldError) string {
	// fe.Field() 已透過 RegisterTagNameFunc 處理，會顯示 'excel' tag (即 Excel 標頭名)
	// fe.Tag() 是驗證規則的名稱, e.g., "required", "min", "max", "oneof"
	// fe.Param() 是驗證規則的參數, e.g., for "min=5", Param() is "5"; for "oneof=0 1", Param() is "0 1"
	// fe.Value() 是導致錯誤的實際值

	tag := fe.Tag()
	param := fe.Param()

	switch tag {
	case "required":
		return "此為必填欄位，不可為空。"
	case "min":
		// 根據欄位類型（字串長度、數字大小、集合元素個數）提供不同訊息
		switch fe.Kind() {
		case reflect.String, reflect.Slice, reflect.Array, reflect.Map:
			return fmt.Sprintf("長度或數量至少需要 %s。", param)
		default: // 數字類型
			return fmt.Sprintf("數值至少需要為 %s。", param)
		}
	case "max":
		switch fe.Kind() {
		case reflect.String, reflect.Slice, reflect.Array, reflect.Map:
			return fmt.Sprintf("長度或數量不可超過 %s。", param)
		default: // 數字類型
			return fmt.Sprintf("數值不可超過 %s。", param)
		}
	case "oneof":
		// 將空格分隔的允許值列表轉換為逗號分隔，更易讀
		allowedValues := strings.ReplaceAll(param, " ", ", ")
		return fmt.Sprintf("值必須是下列之一：%s。", allowedValues)
	case "email":
		return "格式不正確，應為有效的電子郵件地址。"
	case "url":
		return "格式不正確，應為有效的 URL。"
	// 可以為其他常用的 validator tags 添加更多客製化的中文翻譯
	default:
		// 通用回退訊息
		if param != "" {
			return fmt.Sprintf("驗證規則「%s:%s」失敗。", tag, param)
		}
		return fmt.Sprintf("驗證規則「%s」失敗。", tag)
	}
}

// ListProducts 處理產品列表查詢請求
func (h *handler) ListProducts(c echo.Context) error {
	logger := h.logger.Named("ListProducts").With(zap.String("ip", c.RealIP()))
	logger.Info("產品列表查詢請求開始處理")

	// 解析請求資料
	var req ProductListRequest
	if err := c.Bind(&req); err != nil {
		logger.Error("請求資料解析失敗", zap.Error(err))
		return c.JSON(http.StatusBadRequest, APIErrorResponse{Message: "無效的請求資料: " + err.Error()})
	}

	// 驗證請求資料
	if err := h.validator.Struct(req); err != nil {
		logger.Error("請求資料驗證失敗", zap.Error(err))
		return c.JSON(http.StatusBadRequest, APIErrorResponse{Message: "無效的請求參數: " + err.Error()})
	}

	// 設定預設分頁參數
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100 // 限制最大每頁數量
	}

	// 設定預設排序
	if req.SortBy == "" {
		req.SortBy = "item_id"
	}
	if req.SortDir == "" {
		req.SortDir = "asc"
	}

	// 從上下文中獲取使用者ID和角色
	var userID uint32
	var userRole sqlc.UserRole

	if userIDValue := c.Get(models.UserID); userIDValue != nil {
		userID = userIDValue.(uint32)
	} else {
		logger.Error("無法獲取使用者ID")
		return c.JSON(http.StatusUnauthorized, APIErrorResponse{Message: "請先登入"})
	}

	if userRoleValue := c.Get(models.UserRole); userRoleValue != nil {
		userRole = userRoleValue.(sqlc.UserRole)
	} else {
		logger.Error("無法獲取使用者角色")
		return c.JSON(http.StatusUnauthorized, APIErrorResponse{Message: "請先登入"})
	}

	// 計算分頁參數
	offset := (req.Page - 1) * req.PageSize
	limit := req.PageSize

	// 構建查詢參數
	params := sqlc.ListProductsByProjectIDParams{
		Category:       req.Category,
		Brand:          req.Brand,
		SearchTerm:     req.SearchTerm,
		IncludeDeleted: req.IncludeDeleted,
		SortBy:         req.SortBy,
		SortDir:        req.SortDir,
		OffsetVal:      offset,
		LimitVal:       limit,
	}

	// 如果指定了專案ID，則查詢該專案的產品
	var products []*models.Product
	var err error

	if req.ProjectID != nil && *req.ProjectID > 0 {
		// 檢查使用者是否有權限訪問該專案（這裡需要實作權限檢查邏輯）
		// 暫時跳過權限檢查，實際應該調用專案服務檢查權限

		// 查詢專案產品
		products, err = h.product.ListByProjectID(c.Request().Context(), *req.ProjectID, params)
		if err != nil {
			logger.Error("獲取專案產品列表失敗", zap.Error(err))
			return c.JSON(http.StatusInternalServerError, APIErrorResponse{Message: "獲取產品列表失敗"})
		}
	} else {
		// 如果沒有指定專案ID，根據使用者角色決定查詢範圍
		if userRole == sqlc.UserRoleCompany {
			// 廠商用戶只能查看自己參與的專案的產品
			logger.Error("廠商用戶必須指定專案ID")
			return c.JSON(http.StatusBadRequest, APIErrorResponse{Message: "請指定專案ID"})
		}

		// SPO和CISA可以查看所有產品，但需要指定專案ID
		logger.Error("必須指定專案ID")
		return c.JSON(http.StatusBadRequest, APIErrorResponse{Message: "請指定專案ID"})
	}

	// 計算總數（簡化實作，實際應該有專門的計數查詢）
	totalCount := int64(len(products))
	if len(products) == int(limit) {
		// 如果返回的數量等於限制數量，可能還有更多資料
		totalCount = int64(req.Page * req.PageSize) // 這是一個估算值
	}

	// 構建回應
	response := ProductListResponse{
		Total:      totalCount,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: (totalCount + int64(req.PageSize) - 1) / int64(req.PageSize),
		List:       products,
	}

	logger.Info("產品列表查詢成功",
		zap.Int32("page", req.Page),
		zap.Int32("pageSize", req.PageSize),
		zap.Int64("total", totalCount),
		zap.Int("count", len(products)))

	return c.JSON(http.StatusOK, response)
}